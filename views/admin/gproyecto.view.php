<?php
#region region DOCS

/** @var Proyecto $proyecto */
/** @var int $proyectoId */
/** @var string $success_text */
/** @var string $success_display */
/** @var string $error_text */
/** @var string $error_display */

use App\classes\Proyecto;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="es" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Gestión de Proyecto</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="Gestión de proyecto" name="description"/>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/admin/general/head.view.php'; ?>
	<?php #endregion HEAD ?>
</head>

<body>
<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/admin/general/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/admin/general/sidebar.view.php'; ?>

	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<div class="d-flex align-items-center mb-3">
			<div>
				<h4 class="mb-0">Gestión de Proyecto</h4>
				<p class="mb-0 text-muted">
					<?php if ($proyecto): ?>
						<?php echo htmlspecialchars($proyecto->getDescripcion()); ?>
					<?php else: ?>
						Administra los parámetros del proyecto
					<?php endif; ?>
				</p>
			</div>
			<div class="ms-auto">
				<a href="lproyectos" class="btn btn-secondary">
					<i class="fa fa-arrow-left fa-fw me-1"></i> Volver a Proyectos
				</a>
			</div>
		</div>
		<hr>
		<?php #endregion PAGE HEADER ?>

		<?php #region region FLASH MESSAGES ?>
		<?php if ($success_display === 'show'): ?>
			<div class="alert alert-success alert-dismissible fade show" role="alert">
				<strong>¡Éxito!</strong> <?php echo htmlspecialchars($success_text); ?>
				<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
			</div>
		<?php endif; ?>

		<?php if ($error_display === 'show'): ?>
			<div class="alert alert-danger alert-dismissible fade show" role="alert">
				<strong>¡Error!</strong> <?php echo htmlspecialchars($error_text); ?>
				<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
			</div>
		<?php endif; ?>
		<?php #endregion FLASH MESSAGES ?>

		<?php if ($proyecto): ?>
		<?php #region region PROJECT MANAGEMENT CONTENT ?>
		<div class="row">
			<!-- Left Sidebar with Vertical Tabs -->
			<div class="col-md-3">
				<div class="panel panel-inverse no-border-radious">
					<div class="panel-heading no-border-radious">
						<h4 class="panel-title">Navegación</h4>
					</div>
					<div class="panel-body p-0">
						<div class="nav flex-column nav-pills" id="v-pills-tab" role="tablist" aria-orientation="vertical">
							<button class="nav-link active text-start" id="v-pills-inicio-tab" data-bs-toggle="pill" 
									data-bs-target="#v-pills-inicio" type="button" role="tab" 
									aria-controls="v-pills-inicio" aria-selected="true">
								<i class="fa fa-home fa-fw me-2"></i> Inicio
							</button>
							<!-- Future tabs can be added here -->
						</div>
					</div>
				</div>
			</div>

			<!-- Main Content Area -->
			<div class="col-md-9">
				<div class="tab-content" id="v-pills-tabContent">
					
					<?php #region region TAB: INICIO ?>
					<div class="tab-pane fade show active" id="v-pills-inicio" role="tabpanel" 
						 aria-labelledby="v-pills-inicio-tab">
						<div class="panel panel-inverse no-border-radious">
							<div class="panel-heading no-border-radious">
								<h4 class="panel-title">
									<i class="fa fa-info-circle fa-fw me-2"></i> Información del Proyecto
								</h4>
								<div class="panel-heading-btn">
									<a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand">
										<i class="fa fa-expand"></i>
									</a>
									<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse">
										<i class="fa fa-minus"></i>
									</a>
								</div>
							</div>
							<div class="panel-body">
								<div class="row">
									<div class="col-md-6">
										<div class="mb-3">
											<label class="form-label fw-bold">ID del Proyecto:</label>
											<p class="form-control-plaintext"><?php echo htmlspecialchars($proyecto->getId()); ?></p>
										</div>
										<div class="mb-3">
											<label class="form-label fw-bold">Descripción:</label>
											<p class="form-control-plaintext"><?php echo htmlspecialchars($proyecto->getDescripcion()); ?></p>
										</div>
									</div>
									<div class="col-md-6">
										<div class="mb-3">
											<label class="form-label fw-bold">Fecha de Creación:</label>
											<p class="form-control-plaintext"><?php echo htmlspecialchars($proyecto->getFechaCreacion() ?? 'N/A'); ?></p>
										</div>
										<div class="mb-3">
											<label class="form-label fw-bold">Fecha de Inicio:</label>
											<p class="form-control-plaintext"><?php echo htmlspecialchars($proyecto->getFechaInicio() ?? 'N/A'); ?></p>
										</div>
									</div>
								</div>
								<div class="row">
									<div class="col-md-12">
										<div class="mb-3">
											<label class="form-label fw-bold">Estado:</label>
											<p class="form-control-plaintext">
												<?php if ($proyecto->isActivo()): ?>
													<span class="badge bg-success">Activo</span>
												<?php else: ?>
													<span class="badge bg-danger">Inactivo</span>
												<?php endif; ?>
											</p>
										</div>
									</div>
								</div>
								
								<div class="mt-4">
									<h5><i class="fa fa-cogs fa-fw me-2"></i> Acciones Rápidas</h5>
									<div class="d-flex gap-2 flex-wrap">
										<a href="eproyecto?id=<?php echo $proyecto->getId(); ?>" class="btn btn-primary btn-sm">
											<i class="fa fa-edit fa-fw me-1"></i> Editar Proyecto
										</a>
										<a href="lproyectos?action=ver_tareas&proyecto_id=<?php echo $proyecto->getId(); ?>" class="btn btn-info btn-sm">
											<i class="fa fa-tasks fa-fw me-1"></i> Ver Tareas
										</a>
									</div>
								</div>
							</div>
						</div>
					</div>
					<?php #endregion TAB: INICIO ?>
					
					<!-- Future tabs content can be added here -->
					
				</div>
			</div>
		</div>
		<?php #endregion PROJECT MANAGEMENT CONTENT ?>
		<?php endif; ?>

	</div>
	<!-- END #content -->

	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top">
		<i class="fa fa-angle-up"></i>
	</a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/admin/general/core_js.view.php'; ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
	// Initialize any JavaScript functionality here
	console.log('Project Management page loaded successfully');
});
</script>
<?php #endregion JS ?>

</body>
</html>
